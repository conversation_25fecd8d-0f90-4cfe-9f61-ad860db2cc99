# PocketFlow Hello World

Your first PocketFlow application! This simple example demonstrates how to create a basic PocketFlow app from scratch.

## Project Structure

```
.
├── docs/          # Documentation files
├── utils/         # Utility functions
├── flow.py        # PocketFlow implementation
├── main.py        # Main application entry point
└── README.md      # Project documentation
```

## Setup

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the example:
```bash
python main.py
```

## What This Example Demonstrates

- How to create your first PocketFlow application
- Basic PocketFlow concepts and usage
- Simple example of PocketFlow's capabilities

## Additional Resources

- [PocketFlow Documentation](https://the-pocket.github.io/PocketFlow/) 